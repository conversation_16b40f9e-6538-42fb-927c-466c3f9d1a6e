"""
@Project    :test_demo
@File       :下载飞火动态壁纸.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/4/30 17:45
"""
import os
import re
import asyncio
import time
import aiohttp
import requests

# word = "火灵儿"
# word = "美杜莎"
# word = "紫灵"
# word = "小医仙"
# word = "李慕婉"
# word = "云曦"
word = "汉库克"
path = f'video/{word}'
# 确保 video 目录存在
os.makedirs(path, exist_ok=True)


def get_video_url(word):
    url = f"https://bizhi.hfnuola.com/pc/v/search?w_name={word}&type=1&page=1&page_size=36"

    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'clientidn': "I8A1UCov1Pg13yVDp0S6OIeSOEhGDFNkQO7fOMKYY31wSRexIfvfhYX3WrpAmhn1",
        'osname': "win11",
        'cpu': "Intel(R) Core(TM) i7-14700",
        'clientid': "60:FF:9E:F4:8F:95",
        'fbl': "1920x1080",
        'memory': "31.69",
        'channel': "zizj2",
        'osver': "34209792",
        'client_id': "60:FF:9E:F4:8F:95",
        'frontver': "2024111301",
        'gpu': "Intel(R)%20UHD%20Graphics%20770",
        'v9': "NMhxzfHLSuNFTmJ31cEoHoE1kDBlP2rFYHEOYnHCbhhwSRexIfvfhYX3WrpAmhn1",
        'token': "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.Kv1VpG1tuicI-zJelnMZVc2_SBxOH82YiAnHMR0pwEg",
        'netbar_bool': "j9TGo653du8KJQ6XiUOpGVkluuhwPX4+WwZnaYn4a60=",
        'version': "2.5.0.1",
        'Referer': "https://bizhiweb.hfnuola.com/clientNew/index/dynamicWallpaper",
        'Accept-Language': "zh-CN",
        'Cookie': "Hm_lvt_666647b36f4aa050596674ad8919cb0c=**********; Hm_lpvt_666647b36f4aa050596674ad8919cb0c=**********; HMACCOUNT=01CEED947F25B598; token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.Kv1VpG1tuicI-zJelnMZVc2_SBxOH82YiAnHMR0pwEg; source=4; sourcepagec=0"
    }

    response = requests.get(url, headers=headers)
    video_urls = re.findall(r'"shortVideoUrl":"(.*?)"', response.text)
    return [url.replace('\\', '') for url in video_urls]


# 异步下载视频
async def download_video_async(video_urls):
    async with aiohttp.ClientSession() as session:
        tasks = [asyncio.create_task(download_video(session, url)) for url in video_urls]
        await asyncio.gather(*tasks)


async def download_video(session, url):
    async with session.get(url) as response:
        filename = url.split('/')[-1]
        with open(f'{path}/{filename}', 'wb') as f:
            f.write(await response.read())


if __name__ == '__main__':
    start = time.time()
    video_urls = get_video_url(word)
    print(video_urls)
    # 异步下载视频
    asyncio.run(download_video_async(video_urls))
    print(time.time() - start)
