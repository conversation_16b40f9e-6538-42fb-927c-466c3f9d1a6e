# -*- coding:utf-8 -*-
"""
@Project    :http_server
@File       :mock.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/5/30 10:26
"""
from Crypto.Hash import HMAC
from Crypto.Hash import SHA1
import base64

def get_signature(key, data_map):
    """生成签名"""
    orig_text = generate_orig_text(data_map)
    print(orig_text)
    return hmac_sha1(key, orig_text)

def hmac_sha1(key, data):
    """HmacSHA1签名并Base64编码"""
    key_bytes = key.encode('utf-8')
    data_bytes = data.encode('utf-8')
    hmac_obj = HMAC.new(key_bytes, data_bytes, SHA1)
    raw_hmac = hmac_obj.digest()
    return base64.b64encode(raw_hmac).decode('utf-8')

def generate_orig_text(data_map):
    """生成排序后的原始字符串"""
    if not data_map:
        return ''
    # 按字典键排序并拼接
    sorted_items = sorted(data_map.items())
    parts = [f"{k}={v}" for k, v in sorted_items]
    return ';'.join(parts)

# 示例用法
key = "e64f658cd60fbd4988d82515e5cb81fb"
params = {
    "partnerOrderId": '20001790625',
    "reasonNote": '储值卡退款测试',
    "refundDuty": 3,
    "postagePrice": '0.00',
    "ticketPrice": '170.00'
}

signature = get_signature(key, params)
print(signature)  # 输出: YGd4usoiDYdUnZrrjkecX+IlaKI=
