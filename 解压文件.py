"""
@Project    :test_demo
@File       :解压文件.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/5/8 17:55
"""
import os
import zipfile


def unzip_file(zip_file_path, output_dir):
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.mkdir(output_dir)
    # 解压文件
    with zipfile.ZipFile(zip_file_path, 'r', metadata_encoding='utf-8') as zip_ref:
        zip_ref.extractall(output_dir)
    print(f"文件已解压到 {output_dir}")
    return output_dir


if __name__ == "__main__":
    # 示例用法
    zip_file_path = r"C:\Users\<USER>\Desktop\中影文档\需求文档+用例\play.zip"
    path = '\\'.join(zip_file_path.split("\\")[:-1])
    dir = zip_file_path.split("\\")[-1].split(".")[0]
    output_dir = rf"{path}\{dir}"
    # print(output_dir)
    unzip_file(zip_file_path, output_dir)
