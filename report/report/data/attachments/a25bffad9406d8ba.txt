INFO     unit_tools.log_util.recordlog:conftest.py:7 ---------------接口测试开始---------------
INFO     unit_tools.log_util.recordlog:sendrequests.py:57 接口名称：影讯详情接口
INFO     unit_tools.log_util.recordlog:sendrequests.py:58 请求地址：http://*************:1984/api/app/handler
INFO     unit_tools.log_util.recordlog:sendrequests.py:59 请求方式：POST
INFO     unit_tools.log_util.recordlog:sendrequests.py:60 请求头：{'User-Agent': 'Android/HUAWEI-BRA-AL00-marlin-android9-2.49.0-1080*1920-1080.0*1920.0', 'Content-Type': 'application/x-www-form-urlencoded', 'Host': '*************:1984', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO     unit_tools.log_util.recordlog:sendrequests.py:61 测试用例名：进入某个影讯详情
INFO     unit_tools.log_util.recordlog:sendrequests.py:62 cookies值：None
INFO     unit_tools.log_util.recordlog:sendrequests.py:67 参数类型：json
INFO     unit_tools.log_util.recordlog:sendrequests.py:69 请求参数：{"param": {"head": {"tradeId": "getNewsDetail", "timestamp": "20250418175141", "validCode": "C66CF5C4CBAD5E46FC482AB10478F98A", "appKey": "*********"}, "body": {"newsId": "297"}}}
INFO     unit_tools.log_util.recordlog:apiutils_business.py:208 接口实际返回结果：{"head":{"errCode":"0","errMsg":"操作成功。","tradeId":"getNewsDetail","timestamp":"20250418175141"},"uid":"1913168548192116736","body":{"filmCode":"","image":"http://*************:11280/cnews/upload/base/2025/03/19/67da771209f7b/b.png","newsId":"297","createTime":"2025-03-19 15:48:00","vedio":"{\"video\":{\"url\":\"https://1256211379.vod2.myqcloud.com/55efa42fvodtranscq1256211379/2c0f47f31397757907632814339/v.f100030.mp4\"},\"fileId\":\"1397757907632814339\",\"processd\":1}","tag":"推荐","title":"MV视频","content":"无","views":52}}
INFO     unit_tools.log_util.recordlog:assertion_utils.py:37 状态码断言成功：接口实际返回状态码 200 == 200
INFO     unit_tools.log_util.recordlog:assertion_utils.py:66 包含模式断言成功：预期结果【0】存在于实际结果【0】中
INFO     unit_tools.log_util.recordlog:assertion_utils.py:66 包含模式断言成功：预期结果【操作成功。】存在于实际结果【操作成功。】中
INFO     unit_tools.log_util.recordlog:assertion_utils.py:208 测试成功
INFO     unit_tools.log_util.recordlog:conftest.py:9 ---------------接口测试结束---------------