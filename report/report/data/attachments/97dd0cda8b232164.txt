INFO     unit_tools.log_util.recordlog:conftest.py:7 ---------------接口测试开始---------------
INFO     unit_tools.log_util.recordlog:sendrequests.py:57 接口名称：添加评论接口
INFO     unit_tools.log_util.recordlog:sendrequests.py:58 请求地址：http://*************:1984/api/app/handler
INFO     unit_tools.log_util.recordlog:sendrequests.py:59 请求方式：POST
INFO     unit_tools.log_util.recordlog:sendrequests.py:60 请求头：{'User-Agent': 'Android/HUAWEI-BRA-AL00-marlin-android9-2.49.0-1080*1920-1080.0*1920.0', 'Content-Type': 'application/x-www-form-urlencoded', 'Host': '*************:1984', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO     unit_tools.log_util.recordlog:sendrequests.py:61 测试用例名：添加评论接口
INFO     unit_tools.log_util.recordlog:sendrequests.py:62 cookies值：None
INFO     unit_tools.log_util.recordlog:sendrequests.py:67 参数类型：json
INFO     unit_tools.log_util.recordlog:sendrequests.py:69 请求参数：{"param": {"head": {"tradeId": "submitNewsReview", "timestamp": "20250418175147", "validCode": "375818812F7E13E8107339ECFD19AC75", "appKey": "*********"}, "body": {"sign": "5738281ada04a553e4ff63991938b659", "newsId": "297", "mainCommentID": "0", "userId": "346431306902072451", "replyCommentID": "0", "content": "测试评论UKPL"}}}
INFO     unit_tools.log_util.recordlog:apiutils_business.py:208 接口实际返回结果：{"head":{"errCode":"0","errMsg":"添加成功","tradeId":"submitNewsReview","timestamp":"20250418175147"},"uid":"1913168574859501568","body":{"datetime":"20250418175148","userImageUrl":"","replyCommentId":0,"replySum":0,"commentId":0,"ifZan":0,"zanSum":0,"content":"测试评论UKPL","username":"***********"}}
INFO     unit_tools.log_util.recordlog:assertion_utils.py:37 状态码断言成功：接口实际返回状态码 200 == 200
INFO     unit_tools.log_util.recordlog:assertion_utils.py:66 包含模式断言成功：预期结果【0】存在于实际结果【0】中
INFO     unit_tools.log_util.recordlog:assertion_utils.py:66 包含模式断言成功：预期结果【添加成功】存在于实际结果【添加成功】中
INFO     unit_tools.log_util.recordlog:assertion_utils.py:208 测试成功
INFO     unit_tools.log_util.recordlog:conftest.py:9 ---------------接口测试结束---------------