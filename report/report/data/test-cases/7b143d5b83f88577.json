{"uid": "7b143d5b83f88577", "name": "查询后台影讯列表接口", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "historyId": "231a96de27062395e300a01d659b30e6", "time": {"start": 1744969892488, "stop": 1744969892670, "duration": 182}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1744969887902, "stop": 1744969891486, "duration": 3584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1744969891486, "stop": 1744969892487, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1744969892487, "stop": 1744969892487, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1744969887868, "stop": 1744969887902, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "a9e76095ab31db00", "name": "接口地址", "source": "a9e76095ab31db00.json", "type": "application/json", "size": 48}, {"uid": "79d20c717645cbd3", "name": "接口名称", "source": "79d20c717645cbd3.json", "type": "application/json", "size": 30}, {"uid": "ac240363624a986f", "name": "请求方式", "source": "ac240363624a986f.json", "type": "application/json", "size": 4}, {"uid": "9860b3d11d062259", "name": "请求头", "source": "9860b3d11d062259.json", "type": "application/json", "size": 15}, {"uid": "ed263eda1f6ab6fc", "name": "<PERSON><PERSON>", "source": "ed263eda1f6ab6fc.json", "type": "application/json", "size": 12}, {"uid": "91022b92499c8909", "name": "测试用例名称", "source": "91022b92499c8909.json", "type": "application/json", "size": 27}, {"uid": "ff13ca3f1b347794", "name": "参数类型", "source": "ff13ca3f1b347794.json", "type": "application/json", "size": 9}, {"uid": "fab0166a81d95db4", "name": "请求参数json格式", "source": "fab0166a81d95db4.json", "type": "application/json", "size": 493}, {"uid": "47b90b1040d96236", "name": "请求参数实际入参", "source": "47b90b1040d96236.json", "type": "application/json", "size": 436}, {"uid": "6f0dd6a6f508704b", "name": "接口实际响应信息", "source": "6f0dd6a6f508704b.json", "type": "application/json", "size": 51990}, {"uid": "f7a8b9882863fb32", "name": "状态码断言结果：成功", "source": "f7a8b9882863fb32.txt", "type": "text/plain", "size": 37}, {"uid": "77a9b0394a087f0a", "name": "相等断言结果：成功", "source": "77a9b0394a087f0a.json", "type": "application/json", "size": 53}, {"uid": "f8140e544b7e8933", "name": "相等断言结果：成功", "source": "f8140e544b7e8933.json", "type": "application/json", "size": 77}, {"uid": "6fc5e25eebf29abf", "name": "log", "source": "6fc5e25eebf29abf.txt", "type": "text/plain", "size": 30553}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 14, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1744969892671, "stop": 1744969893672, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1744969892670, "stop": 1744969892671, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "7b143d5b83f88577.json", "parameterValues": []}