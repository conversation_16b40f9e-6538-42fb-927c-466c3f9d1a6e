{"uid": "53c6541874495479", "name": "上架影讯接口", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "historyId": "c9bf3318417db2e0a229da41979dd631", "time": {"start": 1744969896846, "stop": 1744969897118, "duration": 272}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1744969887902, "stop": 1744969891486, "duration": 3584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1744969895844, "stop": 1744969896844, "duration": 1000}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1744969896845, "stop": 1744969896845, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1744969887868, "stop": 1744969887902, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "3ed68a7e7b4d561b", "name": "接口地址", "source": "3ed68a7e7b4d561b.json", "type": "application/json", "size": 56}, {"uid": "451d9c16af0e150f", "name": "接口名称", "source": "451d9c16af0e150f.json", "type": "application/json", "size": 18}, {"uid": "4fd03d3c45e094c1", "name": "请求方式", "source": "4fd03d3c45e094c1.json", "type": "application/json", "size": 4}, {"uid": "577bf4dd6769668e", "name": "请求头", "source": "577bf4dd6769668e.json", "type": "application/json", "size": 15}, {"uid": "8f8ddb2b8b2e2ac8", "name": "<PERSON><PERSON>", "source": "8f8ddb2b8b2e2ac8.json", "type": "application/json", "size": 12}, {"uid": "114f113c550b8fe5", "name": "测试用例名称", "source": "114f113c550b8fe5.json", "type": "application/json", "size": 12}, {"uid": "e8bdfd65de9555a2", "name": "参数类型", "source": "e8bdfd65de9555a2.json", "type": "application/json", "size": 9}, {"uid": "f9e9d37685bfcc96", "name": "请求参数json格式", "source": "f9e9d37685bfcc96.json", "type": "application/json", "size": 86}, {"uid": "a44ddc9aa3692d53", "name": "请求参数实际入参", "source": "a44ddc9aa3692d53.json", "type": "application/json", "size": 57}, {"uid": "c532170f00794298", "name": "接口实际响应信息", "source": "c532170f00794298.json", "type": "application/json", "size": 61}, {"uid": "2a2b100c48c7a62d", "name": "状态码断言结果：成功", "source": "2a2b100c48c7a62d.txt", "type": "text/plain", "size": 37}, {"uid": "6145ff8296c78ce1", "name": "相等断言结果：成功", "source": "6145ff8296c78ce1.json", "type": "application/json", "size": 53}, {"uid": "ebe41d581ff0c1e0", "name": "相等断言结果：成功", "source": "ebe41d581ff0c1e0.json", "type": "application/json", "size": 77}, {"uid": "c37e0ea5302b402b", "name": "log", "source": "c37e0ea5302b402b.txt", "type": "text/plain", "size": 1597}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 14, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1744969897119, "stop": 1744969898119, "duration": 1000}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1744969897118, "stop": 1744969897118, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "53c6541874495479.json", "parameterValues": []}