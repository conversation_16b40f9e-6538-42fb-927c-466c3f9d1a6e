{"uid": "d6e8f1dd8d994d81", "name": "更新点赞接口", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "historyId": "81f97ed06a5d66bcee1502c065917520", "time": {"start": 1744969909952, "stop": 1744969910060, "duration": 108}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1744969887902, "stop": 1744969891486, "duration": 3584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1744969908950, "stop": 1744969909951, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1744969909951, "stop": 1744969909951, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1744969887868, "stop": 1744969887902, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "e929eb20c7b7d35c", "name": "接口地址", "source": "e929eb20c7b7d35c.json", "type": "application/json", "size": 41}, {"uid": "48c51aaaf1b7ad81", "name": "接口名称", "source": "48c51aaaf1b7ad81.json", "type": "application/json", "size": 18}, {"uid": "45a74ba05a705d3e", "name": "请求方式", "source": "45a74ba05a705d3e.json", "type": "application/json", "size": 4}, {"uid": "4cb9b435b447e054", "name": "请求头", "source": "4cb9b435b447e054.json", "type": "application/json", "size": 248}, {"uid": "6838e5b27b4f7410", "name": "<PERSON><PERSON>", "source": "6838e5b27b4f7410.json", "type": "application/json", "size": 12}, {"uid": "1e097540e13d1608", "name": "测试用例名称", "source": "1e097540e13d1608.json", "type": "application/json", "size": 12}, {"uid": "ff52e4ef03a2fb4d", "name": "参数类型", "source": "ff52e4ef03a2fb4d.json", "type": "application/json", "size": 4}, {"uid": "e6f69457dac416b4", "name": "请求参数json格式", "source": "e6f69457dac416b4.json", "type": "application/json", "size": 422}, {"uid": "37427e37441ce98", "name": "请求参数实际入参", "source": "37427e37441ce98.json", "type": "application/json", "size": 450}, {"uid": "1c33c5a137d303f7", "name": "接口实际响应信息", "source": "1c33c5a137d303f7.json", "type": "application/json", "size": 259}, {"uid": "253762a4a4790576", "name": "状态码断言结果：成功", "source": "253762a4a4790576.txt", "type": "text/plain", "size": 37}, {"uid": "665c6a24b2464f71", "name": "包含断言结果：成功", "source": "665c6a24b2464f71.txt", "type": "text/plain", "size": 33}, {"uid": "9c05f4af9487bc36", "name": "包含断言结果：成功", "source": "9c05f4af9487bc36.txt", "type": "text/plain", "size": 61}, {"uid": "29996cbb2094ba32", "name": "log", "source": "29996cbb2094ba32.txt", "type": "text/plain", "size": 2147}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 14, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1744969910062, "stop": 1744969911062, "duration": 1000}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1744969910061, "stop": 1744969910061, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "d6e8f1dd8d994d81.json", "parameterValues": []}