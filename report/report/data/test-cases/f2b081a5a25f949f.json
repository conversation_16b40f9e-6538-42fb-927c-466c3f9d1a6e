{"uid": "f2b081a5a25f949f", "name": "添加评论接口", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "historyId": "56c3b1bd46de7aee0cf0dbecb3980f4a", "time": {"start": 1744969907732, "stop": 1744969907947, "duration": 215}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1744969887902, "stop": 1744969891486, "duration": 3584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1744969906730, "stop": 1744969907730, "duration": 1000}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1744969887868, "stop": 1744969887902, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1744969907730, "stop": 1744969907730, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "d8d966e566cba0da", "name": "接口地址", "source": "d8d966e566cba0da.json", "type": "application/json", "size": 41}, {"uid": "9802bcdbd2d23162", "name": "接口名称", "source": "9802bcdbd2d23162.json", "type": "application/json", "size": 18}, {"uid": "9d7e16b9c7317639", "name": "请求方式", "source": "9d7e16b9c7317639.json", "type": "application/json", "size": 4}, {"uid": "f8265146e28845e6", "name": "请求头", "source": "f8265146e28845e6.json", "type": "application/json", "size": 248}, {"uid": "31a4418cb885d93a", "name": "<PERSON><PERSON>", "source": "31a4418cb885d93a.json", "type": "application/json", "size": 12}, {"uid": "92b227ee326a190e", "name": "测试用例名称", "source": "92b227ee326a190e.json", "type": "application/json", "size": 18}, {"uid": "6d320089e872b16", "name": "参数类型", "source": "6d320089e872b16.json", "type": "application/json", "size": 4}, {"uid": "3d46703db370fe8", "name": "请求参数json格式", "source": "3d46703db370fe8.json", "type": "application/json", "size": 502}, {"uid": "743eddfb7706b016", "name": "请求参数实际入参", "source": "743eddfb7706b016.json", "type": "application/json", "size": 526}, {"uid": "aa7902309357c290", "name": "接口实际响应信息", "source": "aa7902309357c290.json", "type": "application/json", "size": 469}, {"uid": "c4755b43685783f6", "name": "状态码断言结果：成功", "source": "c4755b43685783f6.txt", "type": "text/plain", "size": 37}, {"uid": "feec5ad40973e3b9", "name": "包含断言结果：成功", "source": "feec5ad40973e3b9.txt", "type": "text/plain", "size": 33}, {"uid": "c7ce200054288904", "name": "包含断言结果：成功", "source": "c7ce200054288904.txt", "type": "text/plain", "size": 55}, {"uid": "97dd0cda8b232164", "name": "log", "source": "97dd0cda8b232164.txt", "type": "text/plain", "size": 2343}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 14, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1744969907948, "stop": 1744969908949, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1744969907948, "stop": 1744969907948, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "f2b081a5a25f949f.json", "parameterValues": []}