{"uid": "50cf1e398dbce251", "name": "影讯详情接口", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "historyId": "e58f98386d9e9c61f2515135bedbf53c", "time": {"start": 1744969901368, "stop": 1744969901482, "duration": 114}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1744969887902, "stop": 1744969891486, "duration": 3584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1744969901368, "stop": 1744969901368, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1744969887868, "stop": 1744969887902, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1744969900367, "stop": 1744969901368, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "eb2b8dec54afe6e", "name": "接口地址", "source": "eb2b8dec54afe6e.json", "type": "application/json", "size": 41}, {"uid": "f38062f0d7da7be9", "name": "接口名称", "source": "f38062f0d7da7be9.json", "type": "application/json", "size": 18}, {"uid": "eb03681ea5842119", "name": "请求方式", "source": "eb03681ea5842119.json", "type": "application/json", "size": 4}, {"uid": "838107d9bdc7c3db", "name": "请求头", "source": "838107d9bdc7c3db.json", "type": "application/json", "size": 248}, {"uid": "57ddde0edcf9d6d7", "name": "<PERSON><PERSON>", "source": "57ddde0edcf9d6d7.json", "type": "application/json", "size": 12}, {"uid": "6000baf3d19424d8", "name": "测试用例名称", "source": "6000baf3d19424d8.json", "type": "application/json", "size": 24}, {"uid": "de448bd01ff8a264", "name": "参数类型", "source": "de448bd01ff8a264.json", "type": "application/json", "size": 4}, {"uid": "aa7b5a3026bea3de", "name": "请求参数json格式", "source": "aa7b5a3026bea3de.json", "type": "application/json", "size": 287}, {"uid": "7f8e7b37ec797b49", "name": "请求参数实际入参", "source": "7f8e7b37ec797b49.json", "type": "application/json", "size": 345}, {"uid": "233c838c16b60ce6", "name": "接口实际响应信息", "source": "233c838c16b60ce6.json", "type": "application/json", "size": 707}, {"uid": "963f54abcc3948fe", "name": "状态码断言结果：成功", "source": "963f54abcc3948fe.txt", "type": "text/plain", "size": 37}, {"uid": "7be7432536bf2192", "name": "包含断言结果：成功", "source": "7be7432536bf2192.txt", "type": "text/plain", "size": 33}, {"uid": "c7db5c069d03a0a4", "name": "包含断言结果：成功", "source": "c7db5c069d03a0a4.txt", "type": "text/plain", "size": 61}, {"uid": "a25bffad9406d8ba", "name": "log", "source": "a25bffad9406d8ba.txt", "type": "text/plain", "size": 2438}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 14, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1744969901483, "stop": 1744969901484, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1744969901484, "stop": 1744969902485, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "50cf1e398dbce251.json", "parameterValues": []}