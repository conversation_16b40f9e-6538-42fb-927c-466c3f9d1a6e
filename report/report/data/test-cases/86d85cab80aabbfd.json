{"uid": "86d85cab80aabbfd", "name": "获取影讯评论接口", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "historyId": "a4c79a1f09f7b0ef874548b847c3c4ab", "time": {"start": 1744969903490, "stop": 1744969903606, "duration": 116}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1744969887902, "stop": 1744969891486, "duration": 3584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1744969903490, "stop": 1744969903490, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1744969902488, "stop": 1744969903490, "duration": 1002}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1744969887868, "stop": 1744969887902, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "a1a493a673ec6e26", "name": "接口地址", "source": "a1a493a673ec6e26.json", "type": "application/json", "size": 41}, {"uid": "56cecc665dc3e85c", "name": "接口名称", "source": "56cecc665dc3e85c.json", "type": "application/json", "size": 24}, {"uid": "89c93aa83e3ffaf7", "name": "请求方式", "source": "89c93aa83e3ffaf7.json", "type": "application/json", "size": 4}, {"uid": "c7b652c680bde82d", "name": "请求头", "source": "c7b652c680bde82d.json", "type": "application/json", "size": 248}, {"uid": "5a024896733017ff", "name": "<PERSON><PERSON>", "source": "5a024896733017ff.json", "type": "application/json", "size": 12}, {"uid": "d8911775d79fba1f", "name": "测试用例名称", "source": "d8911775d79fba1f.json", "type": "application/json", "size": 18}, {"uid": "2dc030f7c0d24dee", "name": "参数类型", "source": "2dc030f7c0d24dee.json", "type": "application/json", "size": 4}, {"uid": "da7266562326e397", "name": "请求参数json格式", "source": "da7266562326e397.json", "type": "application/json", "size": 442}, {"uid": "e848e45c1df517e0", "name": "请求参数实际入参", "source": "e848e45c1df517e0.json", "type": "application/json", "size": 450}, {"uid": "fe5226bbf0952c45", "name": "接口实际响应信息", "source": "fe5226bbf0952c45.json", "type": "application/json", "size": 1735}, {"uid": "b2dd3dd02babb197", "name": "状态码断言结果：成功", "source": "b2dd3dd02babb197.txt", "type": "text/plain", "size": 37}, {"uid": "be22caca6df9eace", "name": "包含断言结果：成功", "source": "be22caca6df9eace.txt", "type": "text/plain", "size": 33}, {"uid": "16cda8a5e9d60d97", "name": "包含断言结果：成功", "source": "16cda8a5e9d60d97.txt", "type": "text/plain", "size": 59}, {"uid": "f57e869ccb8221b3", "name": "log", "source": "f57e869ccb8221b3.txt", "type": "text/plain", "size": 2860}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 14, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1744969903610, "stop": 1744969903611, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1744969903613, "stop": 1744969904614, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "86d85cab80aabbfd.json", "parameterValues": []}