{"uid": "47061112cdae7190", "name": "APP影讯列表接口", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "historyId": "a67e7922458d21c07c7597e7a7bda2b9", "time": {"start": 1744969899126, "stop": 1744969899359, "duration": 233}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1744969887902, "stop": 1744969891486, "duration": 3584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1744969899125, "stop": 1744969899126, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1744969898125, "stop": 1744969899125, "duration": 1000}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1744969887868, "stop": 1744969887902, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "495419499eb7a908", "name": "接口地址", "source": "495419499eb7a908.json", "type": "application/json", "size": 41}, {"uid": "fb49b4bf5a9b32bd", "name": "接口名称", "source": "fb49b4bf5a9b32bd.json", "type": "application/json", "size": 21}, {"uid": "cf5eed5339ce356f", "name": "请求方式", "source": "cf5eed5339ce356f.json", "type": "application/json", "size": 4}, {"uid": "ac5c39e0f33898ee", "name": "请求头", "source": "ac5c39e0f33898ee.json", "type": "application/json", "size": 248}, {"uid": "1866ade91f1ec22b", "name": "<PERSON><PERSON>", "source": "1866ade91f1ec22b.json", "type": "application/json", "size": 12}, {"uid": "c7474a42d0ddf762", "name": "测试用例名称", "source": "c7474a42d0ddf762.json", "type": "application/json", "size": 27}, {"uid": "b3759a87099c36b9", "name": "参数类型", "source": "b3759a87099c36b9.json", "type": "application/json", "size": 4}, {"uid": "f3ea2a14da6860bd", "name": "请求参数json格式", "source": "f3ea2a14da6860bd.json", "type": "application/json", "size": 367}, {"uid": "d98c8067671ebd70", "name": "请求参数实际入参", "source": "d98c8067671ebd70.json", "type": "application/json", "size": 395}, {"uid": "b997738f3e8039b", "name": "接口实际响应信息", "source": "b997738f3e8039b.json", "type": "application/json", "size": 20620}, {"uid": "9dca97e0e61f6269", "name": "状态码断言结果：成功", "source": "9dca97e0e61f6269.txt", "type": "text/plain", "size": 37}, {"uid": "584f69c008d6bc71", "name": "包含断言结果：成功", "source": "584f69c008d6bc71.txt", "type": "text/plain", "size": 33}, {"uid": "d69b55666fd2099e", "name": "包含断言结果：成功", "source": "d69b55666fd2099e.txt", "type": "text/plain", "size": 61}, {"uid": "706e37d33c2e965f", "name": "log", "source": "706e37d33c2e965f.txt", "type": "text/plain", "size": 15118}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 14, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1744969899359, "stop": 1744969899359, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1744969899360, "stop": 1744969900361, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "47061112cdae7190.json", "parameterValues": []}