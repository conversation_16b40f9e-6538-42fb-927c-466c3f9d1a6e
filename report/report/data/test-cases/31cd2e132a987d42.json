{"uid": "31cd2e132a987d42", "name": "更新点赞接口", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "historyId": "f6463e80cbbf70c2d54a6f508f4d3bb4", "time": {"start": 1744969905618, "stop": 1744969905726, "duration": 108}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1744969887902, "stop": 1744969891486, "duration": 3584}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1744969905617, "stop": 1744969905617, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1744969904616, "stop": 1744969905617, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1744969887868, "stop": 1744969887902, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "da95004ac6f51733", "name": "接口地址", "source": "da95004ac6f51733.json", "type": "application/json", "size": 41}, {"uid": "a95f060f23b7560d", "name": "接口名称", "source": "a95f060f23b7560d.json", "type": "application/json", "size": 18}, {"uid": "a9f025a795ccb300", "name": "请求方式", "source": "a9f025a795ccb300.json", "type": "application/json", "size": 4}, {"uid": "54589135048734c8", "name": "请求头", "source": "54589135048734c8.json", "type": "application/json", "size": 248}, {"uid": "5d9e70377fcd3f9c", "name": "<PERSON><PERSON>", "source": "5d9e70377fcd3f9c.json", "type": "application/json", "size": 12}, {"uid": "d62a5a67f21772f4", "name": "测试用例名称", "source": "d62a5a67f21772f4.json", "type": "application/json", "size": 6}, {"uid": "b6fef404190b1518", "name": "参数类型", "source": "b6fef404190b1518.json", "type": "application/json", "size": 4}, {"uid": "646df4797448a693", "name": "请求参数json格式", "source": "646df4797448a693.json", "type": "application/json", "size": 422}, {"uid": "8731147fd866caf5", "name": "请求参数实际入参", "source": "8731147fd866caf5.json", "type": "application/json", "size": 450}, {"uid": "afdf891aad8c3187", "name": "接口实际响应信息", "source": "afdf891aad8c3187.json", "type": "application/json", "size": 259}, {"uid": "a17efc2b41b5b2c3", "name": "状态码断言结果：成功", "source": "a17efc2b41b5b2c3.txt", "type": "text/plain", "size": 37}, {"uid": "1fc3d2525ae3a495", "name": "包含断言结果：成功", "source": "1fc3d2525ae3a495.txt", "type": "text/plain", "size": 33}, {"uid": "956bb56d04470c04", "name": "包含断言结果：成功", "source": "956bb56d04470c04.txt", "type": "text/plain", "size": 61}, {"uid": "e7a0a5a50c7b82f0", "name": "log", "source": "e7a0a5a50c7b82f0.txt", "type": "text/plain", "size": 2141}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 14, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1744969905726, "stop": 1744969905726, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1744969905727, "stop": 1744969906728, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "31cd2e132a987d42.json", "parameterValues": []}