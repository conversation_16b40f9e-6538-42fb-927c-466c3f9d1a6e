#!/bin/bash

# 尝试查找Chrome浏览器的路径
chrome_path=$(mdfind -name "Google Chrome.app" | head -n 1 | xargs -n 1 dirname | xargs -I {} dirname {})

# 如果没有找到Chrome，尝试查找Chromium（作为备选）
if [ -z "$chrome_path" ]; then
    chrome_path=$(mdfind -name "Chromium.app" | head -n 1 | xargs -n 1 dirname | xargs -I {} dirname {})
fi

# 如果没有找到Chrome或Chromium，则设置为空
if [ -z "$chrome_path" ]; then
    chrome_path=""
fi

# 启动本地web服务器（假设http_server是一个可用的命令）
echo "start a webserver ..."
./http_server -port 5001 &> /dev/null &  # 在后台启动服务器，并忽略输出
server_pid=$!  # 获取服务器进程的PID

# 根据是否找到Chrome来决定使用哪个浏览器
if [ -n "$chrome_path" ]; then
    echo "Chrome found at: $chrome_path"
    # 构造Chrome的可执行路径（假设在Chrome.app的Contents/MacOS/Google Chrome）
    chrome_executable="$chrome_path/Contents/MacOS/Google Chrome"
    # 如果Chrome是安装版，则使用open命令打开（macOS的默认方式）
    if [ -x "$chrome_executable" ]; then
        open -a "Google Chrome" --args "http://127.0.0.1:5001" &> /dev/null &
    else
        # 如果Chrome路径不正确或不可执行，则回退到使用系统默认浏览器
        open "http://127.0.0.1:5001" &> /dev/null &
    fi
else
    # 如果没有找到Chrome，则使用系统默认浏览器（通常是Safari）
    echo "Chrome not found. Using default browser."
    open "http://127.0.0.1:5001" &> /dev/null &
fi

# 等待服务器进程结束（这里是一个简化的等待方式，实际上可能需要更复杂的逻辑来处理）
# 注意：由于脚本会在服务器进程之后立即继续执行，因此这个等待可能不是你所期望的。
# 如果你希望脚本在浏览器打开后保持打开状态，你可能需要移除或修改这一部分。
wait $server_pid