"""
@Project    :http_server
@File       :http_server.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/4/21 09:38
"""
# -*- coding: utf-8 -*-
# @Time    : 2023/5/24 9:34
# <AUTHOR> ch<PERSON>yinh<PERSON>
# @File    : http_server.py
# @Software: PyCharm
# @Desc:

import http.server
import socketserver
import os
from functools import partial
import sys
import webbrowser
import threading
import time


class HttpServer:
    def __init__(self, bind: str = "127.0.0.1", port: int = 8000, directory=os.getcwd()):
        """
        :param bind: 指定地址，如本地主机
        :param port: 自定义端口号, 服务器默认监听端口是 8000
        :param directory: 指定工作目录, 服务器默认工作目录为当前目录
        """
        self.bind = bind
        self.port = port
        # 获取当前脚本所在目录（report目录）
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # 检查是否存在report子目录下的index.html
        report_index_path = os.path.join(script_dir, "report", "index.html")
        if os.path.exists(report_index_path):
            # 如果存在report/index.html，则以report目录作为根目录
            self.directory = os.path.join(script_dir, "report")
        else:
            # 否则以当前脚本目录作为根目录
            self.directory = script_dir
        args = sys.argv
        for i in range(1, len(args)):
            if args[i] == "-port" and i + 1 < len(args):
                self.port = int(args[i + 1])
            if args[i] == "-dir" and i + 1 < len(args):
                self.directory = args[i + 1]
            if args[i] == "-bind" and i + 1 < len(args):
                self.bind = args[i + 1]

    def open_browser(self):
        """延迟打开浏览器"""
        time.sleep(1)  # 等待服务器启动
        url = f"http://{self.bind}:{self.port}/"
        print(f"正在打开浏览器: {url}")
        webbrowser.open(url)

    def run(self):
        try:
            with socketserver.TCPServer((self.bind, self.port), partial(http.server.SimpleHTTPRequestHandler,
                                                                        directory=self.directory)) as httpd:
                print(
                    f"工作目录：{self.directory}\n"
                    f"Serving HTTP on {self.bind} port {self.port} \n"
                    f"http://{self.bind}:{self.port}/ ..."
                )

                # 在后台线程中打开浏览器
                browser_thread = threading.Thread(target=self.open_browser)
                browser_thread.daemon = True
                browser_thread.start()

                httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nKeyboard interrupt received, exiting.")
            sys.exit(0)


if __name__ == '__main__':
    server = HttpServer()
    server.run()
