"""
@Project    :http_server
@File       :http_server.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/4/21 09:38
"""
# -*- coding: utf-8 -*-
#!/usr/bin/env python
# -*- coding: utf-8 -*-

# !/usr/bin/env python
# -*- coding: utf-8 -*-

import http.server
import socketserver
import os
import webbrowser
from functools import partial
import sys
import logging


class HttpServer:
    def __init__(self, bind: str = "127.0.0.1", port: int = 8000):
        """
        :param bind: 监听地址 (默认 127.0.0.1)
        :param port: 监听端口 (默认 8000)
        """
        # 关键修改：获取项目根目录（无论server.py在项目中的哪个子目录）
        self.project_root = self._get_project_root()

        # 设置默认目录为项目根目录下的 report/
        self.report_dir = os.path.join(self.project_root, "report")
        self.result_dir = os.path.join(self.project_root, "result")

        # 检查 report/index.html
        self.index_path = os.path.join(self.report_dir, "index.html")
        if not os.path.exists(self.index_path):
            self.index_path = None

        # 处理命令行参数
        self.bind = bind
        self.port = port
        args = sys.argv
        for i in range(1, len(args)):
            if args[i] == "-port" and i + 1 < len(args):
                self.port = int(args[i + 1])
            if args[i] == "-bind" and i + 1 < len(args):
                self.bind = args[i + 1]

    def _get_project_root(self):
        """自动获取项目根目录（向上递归查找直到找到包含 report/ 和 result/ 的目录）"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        while True:
            if all(
                    os.path.exists(os.path.join(current_dir, folder))
                    for folder in ["report", "result"]
            ):
                return current_dir
            parent_dir = os.path.dirname(current_dir)
            if parent_dir == current_dir:  # 到达系统根目录仍未找到
                raise FileNotFoundError("无法找到项目根目录（需包含 report/ 和 result/）")
            current_dir = parent_dir

    def run(self):
        """启动HTTP服务器"""
        try:
            os.chdir(self.report_dir)  # 切换到 report/ 目录作为服务根目录
            handler = partial(http.server.SimpleHTTPRequestHandler, directory=self.report_dir)
            with socketserver.TCPServer((self.bind, self.port), handler) as httpd:
                url = f"http://{self.bind}:{self.port}/"
                print(
                    f"项目根目录: {self.project_root}\n"
                    f"服务目录: {self.report_dir}\n"
                    f"监听地址: {self.bind}:{self.port}\n"
                    f"访问URL: {url}\n"
                    f"按 Ctrl+C 停止服务"
                )
                # 自动打开浏览器
                if self.index_path:
                    webbrowser.open(url)
                httpd.serve_forever()
        except Exception as e:
            logging.error("服务器启动失败: %s", str(e))
            raise


if __name__ == '__main__':
    # 配置日志（日志文件会保存在项目根目录）
    logging.basicConfig(
        filename=os.path.join(os.path.dirname(os.path.abspath(__file__)), 'server.log'),
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    try:
        server = HttpServer()
        server.run()
    except Exception as e:
        print(f"错误: {e}\n详细日志见: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'server.log')}")
        logging.exception("程序崩溃")
    finally:
        # 防止双击运行时闪退
        if sys.platform == "win32" and not sys.stdin.isatty():
            input("按 Enter 键退出...")