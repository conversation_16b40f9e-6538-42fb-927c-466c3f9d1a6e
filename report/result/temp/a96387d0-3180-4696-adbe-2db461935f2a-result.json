{"name": "上架影讯接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "07206dca-4e8c-404a-8e3c-5fc745473e98-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "efe38245-496c-411f-8117-48d202be4bed-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "20af74ba-f734-4117-a69d-b921a34e2714-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "861fe266-a029-405b-9fd1-465f23994eaa-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "311f755e-04ba-4b09-ac5a-8bf6235fc848-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "00b96d59-697f-4af2-a694-58c67e70abc5-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "0d4bd717-51d8-4487-a861-05f97d8cedcf-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "8864f0e2-3764-42e0-802c-28fd4ea0d132-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "a53cb3bc-2df8-4032-87c1-ee9318e5b8b9-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "64b4761c-6e1d-4b99-9679-d57e4317bcec-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "578bb72b-87eb-4b5b-aa17-1635c38ca477-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "a03a26a4-824b-4946-8779-453295c79615-attachment.json", "type": "application/json"}, {"name": "相等断言结果：成功", "source": "fb147179-207b-4483-aa71-e91093e638f8-attachment.json", "type": "application/json"}, {"name": "log", "source": "005c1984-704e-4db2-b5b9-0e8f52576edc-attachment.txt", "type": "text/plain"}], "start": 1744969896846, "stop": 1744969897118, "uuid": "3a092930-8a1b-4576-b0ca-95dd28b9578c", "historyId": "c9bf3318417db2e0a229da41979dd631", "testCaseId": "c7fa319afee472e7ccacc9392d96392c", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}]}