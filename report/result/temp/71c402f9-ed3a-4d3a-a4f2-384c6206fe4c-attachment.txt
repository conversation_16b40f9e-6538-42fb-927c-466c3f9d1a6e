INFO     unit_tools.log_util.recordlog:conftest.py:7 ---------------接口测试开始---------------
INFO     unit_tools.log_util.recordlog:sendrequests.py:57 接口名称：获取影讯评论接口
INFO     unit_tools.log_util.recordlog:sendrequests.py:58 请求地址：http://*************:1984/api/app/handler
INFO     unit_tools.log_util.recordlog:sendrequests.py:59 请求方式：POST
INFO     unit_tools.log_util.recordlog:sendrequests.py:60 请求头：{'User-Agent': 'Android/HUAWEI-BRA-AL00-marlin-android9-2.49.0-1080*1920-1080.0*1920.0', 'Content-Type': 'application/x-www-form-urlencoded', 'Host': '*************:1984', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO     unit_tools.log_util.recordlog:sendrequests.py:61 测试用例名：获取影讯评论
INFO     unit_tools.log_util.recordlog:sendrequests.py:62 cookies值：None
INFO     unit_tools.log_util.recordlog:sendrequests.py:67 参数类型：json
INFO     unit_tools.log_util.recordlog:sendrequests.py:69 请求参数：{"param": {"head": {"tradeId": "getNewsReviews", "timestamp": "20250418175143", "validCode": "E58B778E45E88B0534C52C087AE5E6F2", "appKey": "*********"}, "body": {"newsId": "297", "mainCommentID": "0", "numPerPage": "5", "pageIdx": "1", "userId": "", "replyCommentID": "0"}}}
INFO     unit_tools.log_util.recordlog:apiutils_business.py:208 接口实际返回结果：{"head":{"errCode":"0","errMsg":"","tradeId":"getNewsReviews","timestamp":"20250418175143"},"uid":"1913168557012738048","body":{"reviews":[{"datetime":"20250418174656","replyCommentId":0,"replySum":0,"commentId":6062,"zanSum":0,"content":"测试评论MDag","username":"188****4383"},{"datetime":"20250418165019","replyCommentId":0,"replySum":0,"commentId":6061,"zanSum":0,"content":"测试评论mqwY","username":"188****4383"},{"datetime":"20250417172414","replyCommentId":0,"replySum":0,"commentId":6060,"zanSum":0,"content":"测试评论odSy","username":"188****4383"},{"datetime":"20250417170634","replyCommentId":0,"replySum":0,"commentId":6059,"zanSum":0,"content":"测试评论ZFIw","username":"188****4383"},{"datetime":"20250417170014","replyCommentId":0,"replySum":0,"commentId":6058,"zanSum":0,"content":"测试评论XHJT","username":"188****4383"}],"sumComment":17}}
INFO     unit_tools.log_util.recordlog:assertion_utils.py:37 状态码断言成功：接口实际返回状态码 200 == 200
INFO     unit_tools.log_util.recordlog:assertion_utils.py:66 包含模式断言成功：预期结果【0】存在于实际结果【0】中
INFO     unit_tools.log_util.recordlog:assertion_utils.py:66 包含模式断言成功：预期结果【getNewsReviews】存在于实际结果【getNewsReviews】中
INFO     unit_tools.log_util.recordlog:assertion_utils.py:208 测试成功
INFO     unit_tools.log_util.recordlog:conftest.py:9 ---------------接口测试结束---------------