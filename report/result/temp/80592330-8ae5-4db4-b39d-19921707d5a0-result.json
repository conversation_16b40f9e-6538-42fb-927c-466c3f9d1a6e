{"name": "获取影讯评论接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "9f4449f2-326c-48a5-8eb8-ec9db09fb28a-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "789b9446-0263-4bf5-a386-2b7949156ac4-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "7e408e0c-1d8c-4d39-90c1-02f59c5c5071-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "3388ca33-d8bf-454d-b0af-6e5b90b543ad-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "a05ebbb2-28fa-44e7-8c8c-87abb95e3ecd-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "a4385058-b925-4237-bd4b-beaace92da17-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "741bd6b8-848e-4a4e-b46c-0676b824ee32-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "0b68a2ad-b5a5-4efd-886e-f5b2c19667de-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "0a19ab38-ea05-424e-bfb6-761afbfb2b65-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "b0dd13c0-ebee-42b2-8d76-e5590322b045-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "bc23aa9f-7554-41f2-b07e-37d588372685-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "342583cb-b475-452e-a902-128c2c764c22-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "1141daa0-c6a3-4d61-818d-0f69d41d7821-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "71c402f9-ed3a-4d3a-a4f2-384c6206fe4c-attachment.txt", "type": "text/plain"}], "start": 1744969903490, "stop": 1744969903606, "uuid": "3fe0aeb1-2d3b-4694-975a-48bf46991765", "historyId": "a4c79a1f09f7b0ef874548b847c3c4ab", "testCaseId": "c7fa319afee472e7ccacc9392d96392c", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}]}