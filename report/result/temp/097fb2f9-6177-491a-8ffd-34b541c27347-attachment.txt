INFO     unit_tools.log_util.recordlog:conftest.py:7 ---------------接口测试开始---------------
INFO     unit_tools.log_util.recordlog:sendrequests.py:57 接口名称：下架影讯接口
INFO     unit_tools.log_util.recordlog:sendrequests.py:58 请求地址：http://192.168.25.16:40/proxy/cnews/article/editstatusdown
INFO     unit_tools.log_util.recordlog:sendrequests.py:59 请求方式：POST
INFO     unit_tools.log_util.recordlog:sendrequests.py:60 请求头：None
INFO     unit_tools.log_util.recordlog:sendrequests.py:61 测试用例名：下架影讯
INFO     unit_tools.log_util.recordlog:sendrequests.py:62 cookies值：None
INFO     unit_tools.log_util.recordlog:sendrequests.py:67 参数类型：data
INFO     unit_tools.log_util.recordlog:sendrequests.py:69 请求参数："pagination=%7B%22queryParams%22%3A%7B%22id%22%3A277%7D%7D"
INFO     unit_tools.log_util.recordlog:apiutils_business.py:208 接口实际返回结果：{"code":0,"msg":"修改成功","data":277}
INFO     unit_tools.log_util.recordlog:assertion_utils.py:37 状态码断言成功：接口实际返回状态码 200 == 200
INFO     unit_tools.log_util.recordlog:assertion_utils.py:96 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO     unit_tools.log_util.recordlog:assertion_utils.py:96 相等断言成功：接口实际结果 {'msg': '修改成功'} == 预期结果：{'msg': '修改成功'}
INFO     unit_tools.log_util.recordlog:assertion_utils.py:208 测试成功
INFO     unit_tools.log_util.recordlog:conftest.py:9 ---------------接口测试结束---------------