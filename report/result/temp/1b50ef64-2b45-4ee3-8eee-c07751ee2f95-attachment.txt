INFO     unit_tools.log_util.recordlog:conftest.py:7 ---------------接口测试开始---------------
INFO     unit_tools.log_util.recordlog:sendrequests.py:57 接口名称：更新点赞接口
INFO     unit_tools.log_util.recordlog:sendrequests.py:58 请求地址：http://*************:1984/api/app/handler
INFO     unit_tools.log_util.recordlog:sendrequests.py:59 请求方式：POST
INFO     unit_tools.log_util.recordlog:sendrequests.py:60 请求头：{'User-Agent': 'Android/HUAWEI-BRA-AL00-marlin-android9-2.49.0-1080*1920-1080.0*1920.0', 'Content-Type': 'application/x-www-form-urlencoded', 'Host': '*************:1984', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO     unit_tools.log_util.recordlog:sendrequests.py:61 测试用例名：点赞
INFO     unit_tools.log_util.recordlog:sendrequests.py:62 cookies值：None
INFO     unit_tools.log_util.recordlog:sendrequests.py:67 参数类型：json
INFO     unit_tools.log_util.recordlog:sendrequests.py:69 请求参数：{"param": {"head": {"tradeId": "updateNewsCommentZan", "timestamp": "20250418175145", "validCode": "711673F307020C02293011A9498EFE6A", "appKey": "*********"}, "body": {"sign": "fc468bd21c598c7b206a7207f5a32c02", "commentId": "297", "type": "1", "userId": "346431306902072451"}}}
INFO     unit_tools.log_util.recordlog:apiutils_business.py:208 接口实际返回结果：{"head":{"errCode":"0","errMsg":"操作成功。","tradeId":"updateNewsCommentZan","timestamp":"20250418175145"},"uid":"1913168565963382784","body":{"msg":"","result":2}}
INFO     unit_tools.log_util.recordlog:assertion_utils.py:37 状态码断言成功：接口实际返回状态码 200 == 200
INFO     unit_tools.log_util.recordlog:assertion_utils.py:66 包含模式断言成功：预期结果【0】存在于实际结果【0】中
INFO     unit_tools.log_util.recordlog:assertion_utils.py:66 包含模式断言成功：预期结果【操作成功。】存在于实际结果【操作成功。】中
INFO     unit_tools.log_util.recordlog:assertion_utils.py:208 测试成功
INFO     unit_tools.log_util.recordlog:conftest.py:9 ---------------接口测试结束---------------