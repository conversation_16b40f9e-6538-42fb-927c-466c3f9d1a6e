{"name": "影讯详情接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "f7b26c41-8e70-4ac0-812e-543f8bfbfa0b-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "e40a92f2-aaeb-4f2c-bbad-96bccd43d6d2-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "442f226b-ed2d-4d85-b9e5-d79ae7cf4666-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "7d096532-d3d0-46de-ba91-18a545287780-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "762c0191-81df-472e-a7d4-86ddd3cfb253-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "db6ee3ec-8303-4d24-99f9-a12432e6273c-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "fc53b03a-b702-4639-9f8e-82ab21a809d5-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "0cf10266-268e-48dc-8978-423aa5647443-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "a851335b-09f2-4359-b46d-b306c8379344-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "9fa7ffe2-5044-4007-a39e-8796ceccbb79-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "c676a00c-5e8b-48a8-a495-8059136c7248-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "e2bb1bd3-1792-4197-9f27-d601db9d1227-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "7f9cb18a-4aee-4b73-9a29-f8328bee3b34-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "fdffef28-98d2-4e6a-88e2-b4fbff86acee-attachment.txt", "type": "text/plain"}], "start": 1744969901368, "stop": 1744969901482, "uuid": "9c970600-22d5-4230-8347-caa0b20c2a0b", "historyId": "e58f98386d9e9c61f2515135bedbf53c", "testCaseId": "c7fa319afee472e7ccacc9392d96392c", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}]}