{"name": "查询后台影讯列表接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "d9b1b90c-edea-4ace-b588-e3390492e700-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "23dcf1a4-a386-43c1-9f3c-151e12eedad1-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "42cfd6ee-3e5a-455a-bbaa-48508e92fa4c-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "ab123056-a5ec-4702-8d88-a948fa853a9f-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "dd377a9f-2f3c-44b8-8916-4304939bf465-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "fa9e1fb8-515f-427f-9621-81bb3619cbf9-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "bfba65e9-1b1d-40c7-bff9-661f15a68b71-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "777876d3-dcbb-4811-a091-5b3dde93034c-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "88116d55-fdc9-495b-a0ab-7f0c6ec8e3a5-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "a9fbf44d-9651-4623-aef6-d5d406932e86-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "dcf95f4f-b08f-409b-9ac8-35cf38a025ce-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "a2546e14-c54c-48f5-a0bb-1ab2c2f32133-attachment.json", "type": "application/json"}, {"name": "相等断言结果：成功", "source": "faa3ae8b-9626-44f1-9b6b-3fe86e97df24-attachment.json", "type": "application/json"}, {"name": "log", "source": "0f91c372-a408-48aa-a3b0-24c4d90299ce-attachment.txt", "type": "text/plain"}], "start": 1744969892488, "stop": 1744969892670, "uuid": "dc268257-5b43-4548-8af6-483fedc47fea", "historyId": "231a96de27062395e300a01d659b30e6", "testCaseId": "c7fa319afee472e7ccacc9392d96392c", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}]}