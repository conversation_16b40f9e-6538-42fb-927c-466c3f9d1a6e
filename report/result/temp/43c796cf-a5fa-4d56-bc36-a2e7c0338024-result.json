{"name": "APP影讯列表接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "14b81438-e9f0-440a-8df3-b5d798e69885-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "135545d4-7f9a-4703-9217-0dbdf180da08-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "04e9a66f-74b2-485f-a040-7471fa5083f1-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "7410e272-43b6-4616-a788-2d3bf9d139ea-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "5ea64bcf-5dbe-44a8-8f4e-6cec6ca09be9-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "3cb46bac-1a86-4bfd-a144-dfe6e902bade-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "cfa86dce-a08d-4156-82a8-4cdbb445619b-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "11571cc9-0352-4582-adcf-7f6c687e8106-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "0f0497a8-dfd3-42ab-8e80-a7d1c792750d-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "8f9c0c16-bf35-4aa2-9728-6b5b51eaf245-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "af7dc03f-7d79-4c0c-987e-34afbc1a7434-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "c77041d2-7b17-49e8-83e6-8f1ba3b677f6-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "f027ad91-421b-4b31-a3c1-64d177c908b6-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "100e5c03-aacf-411e-92df-5b155b82f7e6-attachment.txt", "type": "text/plain"}], "start": 1744969899126, "stop": 1744969899359, "uuid": "67e51e8e-291d-4e00-a985-65e6c6720355", "historyId": "a67e7922458d21c07c7597e7a7bda2b9", "testCaseId": "c7fa319afee472e7ccacc9392d96392c", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}]}