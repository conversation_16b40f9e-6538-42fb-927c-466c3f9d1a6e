{"name": "更新点赞接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "720eed89-f7b5-4128-bda8-732d9e0e13e2-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "e90be41f-8002-4cd0-9e6b-7d9789be9218-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "315c92d3-13b9-44a6-95be-08c50ea33d9b-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "0bffdef9-b51d-4d0f-a040-dbfd78f1a446-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "5cc8e60f-29a2-4113-9aa1-08c688661967-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "31982eb1-424c-446b-83b2-59a83d477422-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "ff3c1976-649e-44ff-afc2-bb206fd6564a-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "90642c5e-fbda-477a-ac78-d7f9bfb9870e-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "57702620-b689-4f82-ac3d-ee5f82765043-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "8a25fdf0-64dd-4159-9841-4a80b16df4e5-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "0b2a8808-bd15-4fb8-819b-3e37cc5a719f-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "95bad0a2-0df6-4be1-905a-598904979c36-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "ec38877e-a732-44fc-8564-6be87be5cdf8-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "1b50ef64-2b45-4ee3-8eee-c07751ee2f95-attachment.txt", "type": "text/plain"}], "start": 1744969905618, "stop": 1744969905726, "uuid": "fe4c1504-7d90-41aa-88ea-e9abb38a8eec", "historyId": "f6463e80cbbf70c2d54a6f508f4d3bb4", "testCaseId": "c7fa319afee472e7ccacc9392d96392c", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}]}