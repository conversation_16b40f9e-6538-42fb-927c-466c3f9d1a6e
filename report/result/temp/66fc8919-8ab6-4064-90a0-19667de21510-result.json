{"name": "添加评论接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "fa439f37-5c4b-4629-9f8a-09641ae5982a-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "a1dbe782-08a2-451e-b069-d2f45601ba19-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "05bb0bd3-a3ee-44e1-ba9a-8bb223095431-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "171684ff-7d70-4069-8041-76730065e17f-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "37207842-cf6d-4746-bdae-3269c137ec52-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "e9ed558e-4e67-4493-bad8-36b96492846d-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "c5cab260-2b73-46b7-ac78-24598c0614f1-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "0b61812e-dca2-447d-9640-80bd5afc9f95-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "25c5708d-98e0-4764-a3bc-029519e01fb4-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "8fba1f7e-8360-4a60-a9ef-e005da16d04c-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "b3b546d4-1c9f-4948-a066-b9addddd83cd-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "1190bbf5-b97a-4c54-8aeb-50544d7dda5f-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "a1e9db1e-ca38-44ec-9572-c5fef88bc0da-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "441d6b3f-1aba-441a-90b4-468eb45221a1-attachment.txt", "type": "text/plain"}], "start": 1744969907732, "stop": 1744969907947, "uuid": "3668d0b3-9ce6-4458-b56f-5740c25b2d0a", "historyId": "56c3b1bd46de7aee0cf0dbecb3980f4a", "testCaseId": "c7fa319afee472e7ccacc9392d96392c", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}]}