{"name": "下架影讯接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "1460adfd-b098-497f-929f-e4984ce048e3-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "820ddb1a-122a-4364-ab19-40d95a0bde2c-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "c7c55259-a59e-4167-aeb6-c7f07fa8191d-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "1027bece-258b-4d74-af74-4552fa7294f1-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "ce012b30-0019-4a82-ab5a-6b36d62b7ade-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "4daad961-81cb-44ea-b3cc-ac153f67f58d-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "adbdacf5-aec3-48ea-bc8f-62b5a3ba4afe-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "be5179df-dce0-4c12-acab-d751f3160dd3-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "8b8389b0-4745-4008-a6b7-657c21e81558-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "6938a473-7aed-4ae6-9e7b-ff48faaa07ca-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "ce635198-7110-4f3b-96c6-f05f0e2eae24-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "8121caa3-46b7-424b-9c5c-b1bf4514fb3c-attachment.json", "type": "application/json"}, {"name": "相等断言结果：成功", "source": "a27c8bff-b7d5-455c-b9ab-82b73cebf5bf-attachment.json", "type": "application/json"}, {"name": "log", "source": "097fb2f9-6177-491a-8ffd-34b541c27347-attachment.txt", "type": "text/plain"}], "start": 1744969894676, "stop": 1744969894838, "uuid": "7148eec7-85bd-4899-98f3-9ded1145d1a2", "historyId": "7c9e47822671ca0a7b88e540789b21a9", "testCaseId": "c7fa319afee472e7ccacc9392d96392c", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}]}