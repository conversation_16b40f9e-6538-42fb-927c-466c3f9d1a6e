{"name": "更新点赞接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "b4712dfa-7189-47f0-899d-307be014ea16-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "f154791a-14c2-46f8-968f-ba0cd6f78bde-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "c8bc4272-c213-4290-b7f2-9415d983e2b8-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c70369cb-9481-427e-b5e4-5e9acefe609a-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "83be4639-cdc9-4df2-ba20-c0c5878b0dbf-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "5460e634-c673-4d7b-ab11-e9b73345cfc7-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "fe159d95-55e3-462b-b7f0-3ba26c00b89f-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "475c3224-8eda-4781-9d8c-df21af7ef797-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "ecb7eb39-e7b2-480b-a818-c9c692817e04-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "7db081f4-e3fe-4259-be53-2f339462d568-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "6386a1d8-de65-4619-b80e-e4d6235fbab9-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "25615fdc-07e8-4098-95c3-927cebdaae35-attachment.txt", "type": "text/plain"}, {"name": "包含断言结果：成功", "source": "6adf9508-d7af-430d-a536-cdfe52c2e53f-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "2c270915-d4cc-4ba9-81d0-9cc14233b57b-attachment.txt", "type": "text/plain"}], "start": 1744969909952, "stop": 1744969910060, "uuid": "e43dd691-fd10-4b03-9d46-3f6340daf36d", "historyId": "81f97ed06a5d66bcee1502c065917520", "testCaseId": "c7fa319afee472e7ccacc9392d96392c", "fullName": "testcase.video_news.test_video_news.TestVideoNews#test_setup", "labels": [{"name": "feature", "value": "M01_影讯模块接口测试"}, {"name": "story", "value": "C01_影讯模块流程"}, {"name": "parentSuite", "value": "testcase.video_news"}, {"name": "suite", "value": "test_video_news"}, {"name": "subSuite", "value": "TestVideoNews"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "5028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.video_news.test_video_news"}]}