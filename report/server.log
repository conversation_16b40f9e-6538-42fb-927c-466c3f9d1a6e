2025-07-07 10:50:44,317 - ERROR - ����������ʧ��: [Win<PERSON><PERSON><PERSON> 10048] ͨ��ÿ���׽��ֵ�ַ(Э��/�����ַ/�˿�)ֻ����ʹ��һ�Ρ�
2025-07-07 10:50:44,318 - ERROR - �������
Traceback (most recent call last):
  File "C:\vae\python_project\http_server\report\http_server.py", line 99, in <module>
    server.run()
  File "C:\vae\python_project\http_server\report\http_server.py", line 71, in run
    with socketserver.TCPServer((self.bind, self.port), handler) as httpd:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\vae\python3\3.12.6\Lib\socketserver.py", line 457, in __init__
    self.server_bind()
  File "C:\vae\python3\3.12.6\Lib\socketserver.py", line 473, in server_bind
    self.socket.bind(self.server_address)
OSError: [Win<PERSON><PERSON><PERSON> 10048] ͨ��ÿ���׽��ֵ�ַ(Э��/�����ַ/�˿�)ֻ����ʹ��һ�Ρ�
2025-07-07 10:50:53,268 - ERROR - ����������ʧ��: [WinError 10048] ͨ��ÿ���׽��ֵ�ַ(Э��/�����ַ/�˿�)ֻ����ʹ��һ�Ρ�
2025-07-07 10:50:53,268 - ERROR - �������
Traceback (most recent call last):
  File "C:\vae\python_project\http_server\report\http_server.py", line 99, in <module>
    server.run()
  File "C:\vae\python_project\http_server\report\http_server.py", line 71, in run
    with socketserver.TCPServer((self.bind, self.port), handler) as httpd:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\vae\python3\3.12.6\Lib\socketserver.py", line 457, in __init__
    self.server_bind()
  File "C:\vae\python3\3.12.6\Lib\socketserver.py", line 473, in server_bind
    self.socket.bind(self.server_address)
OSError: [WinError 10048] ͨ��ÿ���׽��ֵ�ַ(Э��/�����ַ/�˿�)ֻ����ʹ��һ�Ρ�
2025-07-07 10:55:46,997 - ERROR - �������
Traceback (most recent call last):
  File "C:\vae\python_project\http_server\report\http_server.py", line 77, in <module>
    server.run()
  File "C:\vae\python_project\http_server\report\http_server.py", line 61, in run
    webbrowser.open(path)
                    ^^^^
NameError: name 'path' is not defined
