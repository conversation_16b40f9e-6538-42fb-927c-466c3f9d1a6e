('C:\\vae\\python_project\\http_server\\build\\http_server\\PYZ-00.pyz',
 [('_compat_pickle',
   'C:\\vae\\python3\\3.12.6\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\vae\\python3\\3.12.6\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\vae\\python3\\3.12.6\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\vae\\python3\\3.12.6\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\vae\\python3\\3.12.6\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\vae\\python3\\3.12.6\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\vae\\python3\\3.12.6\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\vae\\python3\\3.12.6\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\vae\\python3\\3.12.6\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\vae\\python3\\3.12.6\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\vae\\python3\\3.12.6\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\vae\\python3\\3.12.6\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\vae\\python3\\3.12.6\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'C:\\vae\\python3\\3.12.6\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\vae\\python3\\3.12.6\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\vae\\python3\\3.12.6\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\vae\\python3\\3.12.6\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'C:\\vae\\python3\\3.12.6\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\vae\\python3\\3.12.6\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\vae\\python3\\3.12.6\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\vae\\python3\\3.12.6\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'C:\\vae\\python3\\3.12.6\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'C:\\vae\\python3\\3.12.6\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'C:\\vae\\python3\\3.12.6\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\vae\\python3\\3.12.6\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'C:\\vae\\python3\\3.12.6\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\vae\\python3\\3.12.6\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\vae\\python3\\3.12.6\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\vae\\python3\\3.12.6\\Lib\\hashlib.py', 'PYMODULE'),
  ('html', 'C:\\vae\\python3\\3.12.6\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\vae\\python3\\3.12.6\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'C:\\vae\\python3\\3.12.6\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\vae\\python3\\3.12.6\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.server', 'C:\\vae\\python3\\3.12.6\\Lib\\http\\server.py', 'PYMODULE'),
  ('importlib',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\vae\\python3\\3.12.6\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\vae\\python3\\3.12.6\\Lib\\ipaddress.py', 'PYMODULE'),
  ('logging',
   'C:\\vae\\python3\\3.12.6\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\vae\\python3\\3.12.6\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\vae\\python3\\3.12.6\\Lib\\mimetypes.py', 'PYMODULE'),
  ('numbers', 'C:\\vae\\python3\\3.12.6\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\vae\\python3\\3.12.6\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'C:\\vae\\python3\\3.12.6\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\vae\\python3\\3.12.6\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\vae\\python3\\3.12.6\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\vae\\python3\\3.12.6\\Lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'C:\\vae\\python3\\3.12.6\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\vae\\python3\\3.12.6\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'C:\\vae\\python3\\3.12.6\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'C:\\vae\\python3\\3.12.6\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\vae\\python3\\3.12.6\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\vae\\python3\\3.12.6\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\vae\\python3\\3.12.6\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'C:\\vae\\python3\\3.12.6\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\vae\\python3\\3.12.6\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\vae\\python3\\3.12.6\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\vae\\python3\\3.12.6\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\vae\\python3\\3.12.6\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'C:\\vae\\python3\\3.12.6\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\vae\\python3\\3.12.6\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\vae\\python3\\3.12.6\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\vae\\python3\\3.12.6\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\vae\\python3\\3.12.6\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\vae\\python3\\3.12.6\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\vae\\python3\\3.12.6\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'C:\\vae\\python3\\3.12.6\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'C:\\vae\\python3\\3.12.6\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse',
   'C:\\vae\\python3\\3.12.6\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\vae\\python3\\3.12.6\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\vae\\python3\\3.12.6\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\vae\\python3\\3.12.6\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
