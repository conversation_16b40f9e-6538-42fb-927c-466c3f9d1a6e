(['C:\\vae\\python_project\\http_server\\report\\http_server.py'],
 ['C:\\vae\\python_project\\http_server\\report'],
 [],
 [('C:\\vae\\python_project\\http_server\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\vae\\python_project\\http_server\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\vae\\python_project\\http_server\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('http_server',
   'C:\\vae\\python_project\\http_server\\report\\http_server.py',
   'PYSOURCE')],
 [('zipfile',
   'C:\\vae\\python3\\3.12.6\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\vae\\python3\\3.12.6\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\vae\\python3\\3.12.6\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib', 'C:\\vae\\python3\\3.12.6\\Lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'C:\\vae\\python3\\3.12.6\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib', 'C:\\vae\\python3\\3.12.6\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('ipaddress', 'C:\\vae\\python3\\3.12.6\\Lib\\ipaddress.py', 'PYMODULE'),
  ('fnmatch', 'C:\\vae\\python3\\3.12.6\\Lib\\fnmatch.py', 'PYMODULE'),
  ('contextlib', 'C:\\vae\\python3\\3.12.6\\Lib\\contextlib.py', 'PYMODULE'),
  ('argparse', 'C:\\vae\\python3\\3.12.6\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\vae\\python3\\3.12.6\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'C:\\vae\\python3\\3.12.6\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\vae\\python3\\3.12.6\\Lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'C:\\vae\\python3\\3.12.6\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'C:\\vae\\python3\\3.12.6\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\vae\\python3\\3.12.6\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'C:\\vae\\python3\\3.12.6\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\vae\\python3\\3.12.6\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\vae\\python3\\3.12.6\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\vae\\python3\\3.12.6\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\vae\\python3\\3.12.6\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\vae\\python3\\3.12.6\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\vae\\python3\\3.12.6\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\vae\\python3\\3.12.6\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'C:\\vae\\python3\\3.12.6\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'C:\\vae\\python3\\3.12.6\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\vae\\python3\\3.12.6\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\vae\\python3\\3.12.6\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\vae\\python3\\3.12.6\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'C:\\vae\\python3\\3.12.6\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'C:\\vae\\python3\\3.12.6\\Lib\\bisect.py', 'PYMODULE'),
  ('importlib._abc',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\vae\\python3\\3.12.6\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\vae\\python3\\3.12.6\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils', 'C:\\vae\\python3\\3.12.6\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\vae\\python3\\3.12.6\\Lib\\calendar.py', 'PYMODULE'),
  ('datetime', 'C:\\vae\\python3\\3.12.6\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\vae\\python3\\3.12.6\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\vae\\python3\\3.12.6\\Lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'C:\\vae\\python3\\3.12.6\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\vae\\python3\\3.12.6\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'C:\\vae\\python3\\3.12.6\\Lib\\quopri.py', 'PYMODULE'),
  ('email', 'C:\\vae\\python3\\3.12.6\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\vae\\python3\\3.12.6\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'C:\\vae\\python3\\3.12.6\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'C:\\vae\\python3\\3.12.6\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\vae\\python3\\3.12.6\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'C:\\vae\\python3\\3.12.6\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'C:\\vae\\python3\\3.12.6\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'C:\\vae\\python3\\3.12.6\\Lib\\bz2.py', 'PYMODULE'),
  ('threading', 'C:\\vae\\python3\\3.12.6\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\vae\\python3\\3.12.6\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('struct', 'C:\\vae\\python3\\3.12.6\\Lib\\struct.py', 'PYMODULE'),
  ('shutil', 'C:\\vae\\python3\\3.12.6\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\vae\\python3\\3.12.6\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\vae\\python3\\3.12.6\\Lib\\gzip.py', 'PYMODULE'),
  ('importlib.util',
   'C:\\vae\\python3\\3.12.6\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\vae\\python3\\3.12.6\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'C:\\vae\\python3\\3.12.6\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\vae\\python3\\3.12.6\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\vae\\python3\\3.12.6\\Lib\\ast.py', 'PYMODULE'),
  ('stringprep', 'C:\\vae\\python3\\3.12.6\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\vae\\python3\\3.12.6\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'C:\\vae\\python3\\3.12.6\\Lib\\_py_abc.py', 'PYMODULE'),
  ('subprocess', 'C:\\vae\\python3\\3.12.6\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'C:\\vae\\python3\\3.12.6\\Lib\\signal.py', 'PYMODULE'),
  ('socketserver',
   'C:\\vae\\python3\\3.12.6\\Lib\\socketserver.py',
   'PYMODULE'),
  ('http.server', 'C:\\vae\\python3\\3.12.6\\Lib\\http\\server.py', 'PYMODULE'),
  ('http', 'C:\\vae\\python3\\3.12.6\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('mimetypes', 'C:\\vae\\python3\\3.12.6\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.client', 'C:\\vae\\python3\\3.12.6\\Lib\\http\\client.py', 'PYMODULE'),
  ('ssl', 'C:\\vae\\python3\\3.12.6\\Lib\\ssl.py', 'PYMODULE'),
  ('html', 'C:\\vae\\python3\\3.12.6\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\vae\\python3\\3.12.6\\Lib\\html\\entities.py',
   'PYMODULE')],
 [('python312.dll', 'C:\\vae\\python3\\3.12.6\\python312.dll', 'BINARY'),
  ('unicodedata.pyd',
   'C:\\vae\\python3\\3.12.6\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\vae\\python3\\3.12.6\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll',
   'C:\\vae\\python3\\3.12.6\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll', 'C:\\vae\\python3\\3.12.6\\DLLs\\libssl-3.dll', 'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\vae\\python_project\\http_server\\build\\http_server\\base_library.zip',
   'DATA')])
