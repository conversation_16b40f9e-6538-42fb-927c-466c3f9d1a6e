('C:\\vae\\python_project\\http_server\\build\\http_server\\http_server.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\vae\\python_project\\http_server\\build\\http_server\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\vae\\python_project\\http_server\\build\\http_server\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\vae\\python_project\\http_server\\build\\http_server\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\vae\\python_project\\http_server\\build\\http_server\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\vae\\python_project\\http_server\\build\\http_server\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\vae\\python_project\\http_server\\build\\http_server\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\vae\\python_project\\http_server\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\vae\\python_project\\http_server\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('http_server',
   'C:\\vae\\python_project\\http_server\\report\\http_server.py',
   'PYSOURCE'),
  ('python312.dll', 'C:\\vae\\python3\\3.12.6\\python312.dll', 'BINARY'),
  ('unicodedata.pyd',
   'C:\\vae\\python3\\3.12.6\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\vae\\python3\\3.12.6\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\vae\\python3\\3.12.6\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll',
   'C:\\vae\\python3\\3.12.6\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll', 'C:\\vae\\python3\\3.12.6\\DLLs\\libssl-3.dll', 'BINARY'),
  ('base_library.zip',
   'C:\\vae\\python_project\\http_server\\build\\http_server\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
